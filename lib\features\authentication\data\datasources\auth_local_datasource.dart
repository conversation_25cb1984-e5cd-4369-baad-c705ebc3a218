import 'dart:convert';
import 'package:Kairos/core/device_info.dart';
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

/// Abstract interface for authentication local data source
abstract class AuthLocalDataSource {
  /// Cache user data locally
  Future<void> cacheUser(DeviceInfo user);

  /// Get cached user data
  Future<DeviceInfo> getCachedUser();

  /// Check if user is authenticated
  Future<bool> isAuthenticated();

  /// Clear all cached data
  Future<void> clearCache();

  /// Cache authentication token
  Future<void> cacheToken(String token);

  /// Get cached authentication token
  Future<String?> getCachedToken();

  /// Cache device information locally
  Future<void> cacheDeviceInfo(Map<String, dynamic> deviceInfo);



/// Cache full name locally
  Future<void> saveFullName(String fullName);

  /// Get cached full name
  String? getFullName();

  /// Cache phone number locally
  Future<void> savePhoneNumber(String phoneNumber);

  /// Get cached device information
  Future<Map<String, dynamic>?> getCachedDeviceInfo(); // Allow null return

  /// Get cached phone number
  Future<String?> getPhoneNumber();
}

/// Implementation of AuthLocalDataSource
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences sharedPreferences;

  AuthLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> cacheUser(DeviceInfo user) async {
    // TODO: Implement local storage
    throw UnimplementedError('Cache user not implemented yet');
  }

  @override
  Future<DeviceInfo> getCachedUser() async {
    // TODO: Implement local storage retrieval
    throw UnimplementedError('Get cached user not implemented yet');
  }

  @override
  Future<bool> isAuthenticated() async {
    // TODO: Check if user is authenticated
    throw UnimplementedError('Is authenticated check not implemented yet');
  }

@override
  Future<void> clearCache() async {
    // Get all keys currently stored in sharedPreferences.
    final Set<String> keys = sharedPreferences.getKeys();

    // Iterate through each key.
    for (String key in keys) {
      // Check if the key is NOT 'deviceInfo'.
      if (key != 'deviceInfo') {
        // Remove the key and its corresponding value from sharedPreferences.
        await sharedPreferences.remove(key);
      }
    }
    debugPrint("All data removed from local sharedPreferences ---> ");
  }

  @override
  Future<void> cacheToken(String token) async {
    await sharedPreferences.setString('jwt_token', token);
  }

  @override
  Future<String?> getCachedToken() async {
    return sharedPreferences.getString('jwt_token');
  }

  @override
  Future<void> cacheDeviceInfo(Map<String, dynamic> deviceInfo) async {
    final deviceInfoJson = jsonEncode(deviceInfo);
    await sharedPreferences.setString('deviceInfo', deviceInfoJson);
  }

  @override
  Future<Map<String, dynamic>> getCachedDeviceInfo() async {
    if(sharedPreferences.containsKey('deviceInfo')){

    final deviceInfoJson = sharedPreferences.getString('deviceInfo');
    final deviceInfo = jsonDecode(deviceInfoJson!);
    return deviceInfo;
    } else {
      return {
        'marqueTelephone': DeviceInfoService.deviceInfo.marqueTelephone,
        'modelTelephone': DeviceInfoService.deviceInfo.modelTelephone,
        'imeiTelephone': DeviceInfoService.deviceInfo.imeiTelephone,
        'numeroSerie': DeviceInfoService.deviceInfo.numeroSerie,
      };
      }
  }

  @override
  Future<void> saveFullName(String fullName) async {
    await sharedPreferences.setString('nomComplet', fullName);
  }

  @override
  String? getFullName() {
    if(sharedPreferences.containsKey('nomComplet')){
      return sharedPreferences.getString('nomComplet');
    } else {
      return ""; // !TODO : for testing purposes only, remove this before building app
    }
  }

  @override
  Future<void> savePhoneNumber(String phoneNumber) async {
    await sharedPreferences.setString('numeroTelephone', phoneNumber);
  }

  @override
  Future<String?> getPhoneNumber() async {
    if(sharedPreferences.containsKey('numeroTelephone')){
      return sharedPreferences.getString('numeroTelephone');
    } else {
      return "+221785215684"; // !TODO : for testing purposes only, remove this before building app
    }
  }
}
