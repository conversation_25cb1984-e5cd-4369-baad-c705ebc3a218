import 'package:Kairos/features/student_records/domain/entities/dossier_entity.dart';
import 'package:Kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';
import 'package:equatable/equatable.dart';


abstract class DossiersState extends Equatable {
  const DossiersState();

  @override
  List<Object> get props => [];
}

class DossiersInitial extends DossiersState {}

class DossiersLoading extends DossiersState {}

class DossiersLoaded extends DossiersState {
  final List<DossierEntity> dossiers;

  const DossiersLoaded(this.dossiers);

  @override
  List<Object> get props => [dossiers];
}

class DossiersError extends DossiersState {
  final String message;

  const DossiersError(this.message);

  @override
  List<Object> get props => [message];
}

// Attachment-specific states
class AttachmentLoading extends DossiersState {}

class AttachmentLoaded extends DossiersState {
  final DossierAttachmentEntity attachment;
  final String filePath; // Local file path where the attachment was saved

  const AttachmentLoaded(this.attachment, this.filePath);

  @override
  List<Object> get props => [attachment, filePath];
}

class AttachmentError extends DossiersState {
  final String message;

  const AttachmentError(this.message);

  @override
  List<Object> get props => [message];
}